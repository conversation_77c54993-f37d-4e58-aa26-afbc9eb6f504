<?php

namespace App\Http\Controllers;

use App\Models\Register;
use Illuminate\Http\Request;
use App\Models\Event;
use App\Models\EventDocument;
use App\Models\EventSession;
use App\Models\EventModeratorSpearker;
use App\Models\EventDate;
use App\Models\Country;
use App\Models\TimeZone;
use App\Models\BusinessUnit;
use App\Models\SessionRegister;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class EventController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function new_event(Request $request)
    {
        // marketo_event_id => YYMMXXX formatında
        $time_zones = TimeZone::all()->sortBy('time_zone');
        $countries = Country::all();
        $business_units = BusinessUnit::all();

        $last_event = Event::query()
            ->whereYear('created_at', date('Y'))
            ->whereMonth('created_at', date('m'))
            ->whereDay('created_at', date('d'))
            ->orderBy('id', 'desc')
            ->first();
        if (!$last_event) {
            $marketo_event_id = date('ymd') . '0001';
        } else {
            $marketo_event_id = date('ymd') . str_pad((int) substr($last_event->marketo_event_id, -4) + 1, 3, '0', STR_PAD_LEFT);
        }

        $clone_id = $request->query('clone');
        if ($clone_id) {
            $eventClone = Event::query()->findOrFail($clone_id);
            return view('event.new_event', compact('marketo_event_id', 'eventClone', 'time_zones', 'countries', 'business_units'));
        }

        return view('event.new_event', compact('marketo_event_id', 'time_zones', 'countries', 'business_units'));
    }

    public function store(Request $request)
    {
        //dd($request->all());
        $validated = $request->validate([
            'marketo_event_id' => 'required|unique:events,marketo_event_id',
        ], [
            'marketo_event_id.unique' => 'You cannot submit the form again.',
        ]);

        $cloneStatus = false;
        if ($request->clone_id) {
            $eventClone = Event::query()->find($request->clone_id);
            $cloneStatus = true;
        }
        $file = $request->file('event_banner');
        /*$event_banner_file_name = "";
        if ($cloneStatus) {
            $event_banner_file_name = $eventClone->event_banner;
        } else {*/
        if ($file) {

            $request->validate([
                'event_banner' => 'file|mimes:jpeg,png,jpg,gif|max:2048', // max:2048 -> 2MB
            ]);

            $event_banner_file_name = date('His') . '_' . Str::slug($request->event_title, '_') . '_' . $request->marketo_event_id . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('public/event_banners', $event_banner_file_name);
        } else {
            if ($cloneStatus) {
                $event_banner_file_name = $eventClone->event_banner;
            } else {
                $event_banner_file_name = NULL;
            }
        }
        //}
        /*$time = explode(' - ', $request->event_time);
        $start_time = $time[0];
        $end_time = $time[1];*/

        $event = new Event();
        $event->marketo_event_id = $request->marketo_event_id;
        $event->duration_pf_event = $request->duration_pf_event;
        $event->event_description = $request->event_description;
        $event->contact_email = $request->contact_email;
        /*if ($request->duration_pf_event == 2) {
            $dates = explode(' - ', $request->event_date);
            $event->event_date = \DateTime::createFromFormat('d/m/Y', $dates[0])->format('Y-m-d');
            $event->event_end_date = \DateTime::createFromFormat('d/m/Y', $dates[1])->format('Y-m-d');
        } else {
            $event->event_date = \DateTime::createFromFormat('d/m/Y', $request->event_date)->format('Y-m-d');
        }*/
        //$event->event_start_time = $start_time;
        //$event->event_end_time = $end_time;
        $event->event_banner = $event_banner_file_name;
        $event->event_title = $request->event_title;
        //$event->event_location = $request->event_location;
        $event->business_unit = $request->business_unit;
        $event->smart_list_id = is_numeric($request->smart_list_id) ? (int) $request->smart_list_id : null;
        $event->attendee_quota = $request->attendee_quota;
        $event->moderators_count = $request->moderators_count ?? 0;
        $event->speakers_count = $request->speakers_count ?? 0;
        $event->status = $request->submit_type; // default active
        $event->user_check_in = $request->user_check_in ? 1 : 0;
        $event->badge_creation = $request->badge_creation ? 1 : 0;
        $event->attendees_info = $request->attendees_info ? 1 : 0;
        $event->is_completed = 1;
        $event->save();

        foreach ($request->event_date as $key => $date) {
            $event_dates = new EventDate();
            $event_dates->event_id = $event->id;

            $event_dates->event_date = \DateTime::createFromFormat('d/m/Y', $date)->format('Y-m-d');

            $time = explode(' - ', $request->event_time[$key]);
            $start_time = $time[0];
            $end_time = $time[1];
            $event_dates->event_start_time = $start_time;
            $event_dates->event_end_time = $end_time;

            $event_dates->event_date_timezone = $request->event_date_timezones[$key];
            $event_dates->event_location = $request->event_location[$key];
            $event_dates->event_location_url = $request->event_location_url[$key];
            $event_dates->event_country = $request->event_country[$key];

            $event_dates->save();
        }

        $multiple_document_name = $request->multiple_document_name ? explode(',', $request->multiple_document_name) : null;
        $multiple_document_type = $request->multiple_document_type ? explode(',', $request->multiple_document_type) : null;
        $multiple_selected_session = $request->multiple_selected_session ? explode(',', $request->multiple_selected_session) : null;

        if ($multiple_selected_session) {
            foreach ($multiple_selected_session as $key => $item) {
                $file = $request->file('duplicated_document')[$key];
                $doc_file_name = Str::slug($multiple_document_name[$key], '_') . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('public/event_documents', $doc_file_name);

                $event_documents = new EventDocument;
                $event_documents->event_id = $event->id;
                $event_documents->document_name = $doc_file_name;
                $event_documents->document_type = $multiple_document_type[$key];
                $event_documents->uploaded_document = $request->duplicated_document[$key];
                $event_documents->selected_session = $multiple_selected_session[$key];
                $event_documents->save();
            }
        }

        $session_time_duration_input = $request->session_time_duration_input ? explode(',', $request->session_time_duration_input) : null;
        $session_title_input = $request->session_title_input ? explode(',', $request->session_title_input) : null;
        $session_speakers_input = $request->session_speakers_input ? explode(',', $request->session_speakers_input) : null;
        $session_moderators_input = $request->session_moderators_input ? explode(',', $request->session_moderators_input) : null;
        $session_dates_input = $request->session_dates_input ? explode(',', $request->session_dates_input) : null;

        if ($session_title_input) {
            foreach ($session_title_input as $key => $item) {
                $event_sessions = new EventSession;
                $event_sessions->event_id = $event->id;
                $event_sessions->session_time_duration = $session_time_duration_input[$key];
                $event_sessions->session_title = $session_title_input[$key];
                $event_sessions->session_speakers = isset($session_speakers_input[$key]) ? str_replace('<br>', '-', ltrim($session_speakers_input[$key])) : NULL;
                $event_sessions->session_moderators = isset($session_moderators_input[$key]) ? str_replace('<br>', '-', ltrim($session_moderators_input[$key])) : NULL;
                $event_sessions->session_dates = $request->session_dates_input ? $session_dates_input[$key] : '';
                $event_sessions->save();
            }
        }

        if ($request->speaker_name) {

            if ($request->speaker_name[0]) {
                foreach ($request->speaker_name as $key => $item) {

                    $event_speaker = new EventModeratorSpearker;
                    $doc_file_names = '';
                    if ($cloneStatus) {
                        // clone alınan eventteki speakerları kopyala
                        $eventCloneSpeakerImage = $eventClone->moderator_speaker()->where('type', 1)->get()->toArray()[$key]['banner'];
                        $doc_file_names = Str::slug(date('His') . '_' . $event->id . '_' . $request->speaker_name[$key], '_') . '.' . pathinfo($eventCloneSpeakerImage, PATHINFO_EXTENSION);
                        $path = Storage::copy('public/evet_moderator_speaker/' . $eventCloneSpeakerImage, 'public/evet_moderator_speaker/' . $doc_file_names);
                    } else {
                        if ($request->file('speaker_banner')[$key]) {
                            $file = $request->file('speaker_banner')[$key];
                            $doc_file_names = Str::slug(date('His') . '_' . $event->id . '_' . $request->speaker_name[$key], '_') . '.' . $file->getClientOriginalExtension();
                            $path = $file->storeAs('public/evet_moderator_speaker', $doc_file_names);
                        }
                    }
                    $event_speaker->event_id = $event->id;
                    $event_speaker->type = 1;
                    $event_speaker->name = $request->speaker_name[$key];
                    $event_speaker->title = $request->speaker_title[$key];
                    $event_speaker->banner = Str::limit($doc_file_names, 255, '');
                    $event_speaker->description = $request->speaker_description[$key];
                    $event_speaker->save();
                }
            }
        }

        if ($request->moderator_name) {

            if ($request->moderator_name[0]) {

                foreach ($request->moderator_name as $key => $item) {

                    $event_moderator = new EventModeratorSpearker;
                    $doc_file_namem = '';
                    if ($cloneStatus) {
                        // clone alınan eventteki moderatorleri kopyala
                        $eventCloneModeratorImage = $eventClone->moderator_speaker()->where('type', 2)->get()->toArray()[$key]['banner'];
                        $doc_file_namem = Str::slug(date('His') . '_' . $event->id . '_' . $request->moderator_name[$key], '_') . '.' . pathinfo($eventCloneModeratorImage, PATHINFO_EXTENSION);
                        $path = Storage::copy('public/evet_moderator_moderator/' . $eventCloneModeratorImage, 'public/evet_moderator_moderator/' . $doc_file_namem);
                    } else {
                        if ($request->file('moderator_banner')[$key]) {
                            $file = $request->file('moderator_banner')[$key];
                            $doc_file_namem = Str::slug(date('His') . '_' . $event->id . '_' . $request->moderator_name[$key], '_') . '.' . $file->getClientOriginalExtension();
                            $path = $file->storeAs('public/evet_moderator_moderator', $doc_file_namem);
                        }
                    }

                    $event_moderator->event_id = $event->id;
                    $event_moderator->type = 2;
                    $event_moderator->name = $request->moderator_name[$key];
                    $event_moderator->title = $request->moderator_title[$key];
                    $event_moderator->banner = Str::limit($doc_file_namem, 255, '');
                    $event_moderator->description = $request->moderator_description[$key];
                    $event_moderator->save();
                }
            }
        }

        if ($request->smart_list_id) {
            $users = get_user_list($request->smart_list_id);
            if ($users) {
                foreach ($users as $user) {
                    $uuid = base64_encode($user->email);

                    $user = Register::query()->updateOrCreate(
                        ['user_id' => $user->id, 'event_id' => $event->id],
                        [
                            'uuid' => $uuid,
                            'event_id' => $event->id,
                            'user_id' => $user->id,
                            'first_name' => $user->firstName,
                            'last_name' => $user->lastName,
                            'company' => $user->company,
                            'email' => $user->email,
                            'title' => $user->title,
                            'phone' => $user->phone,
                            'status' => 0,
                            'check_in' => 0
                        ]
                    );
                }

                $short_list = Register::where('event_id', $event->id)
                    ->orderBy('id') // sırayla seç
                    ->limit($event->attendee_quota)
                    ->get();
                if ($short_list) {
                    foreach ($short_list as $user) {
                        $user->status = 1;
                        $user->save();
                    }
                }
            }
        }

        if ($request->submit_type == 1) {
            return redirect('/admin/event/' . $event->id . '/register-list')->with('success', "Your event has been created.");
        } else {
            return redirect('/admin/event-detail/' . $event->id)->with('success', 'Your event has been created.');
        }
    }

    public function update(Request $request)
    {
        //dd($request->all());

        $file = $request->file('event_banner');
        if ($file) {

            $request->validate([
                'event_banner' => 'file|mimes:jpeg,png,jpg,gif|max:2048', // max:2048 -> 2MB
            ]);

            $event_banner_file_name = date('His') . '_' . Str::slug($request->event_title, '_') . '_' . $request->marketo_event_id . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('public/event_banners', $event_banner_file_name);
        }

        /*$time = explode(' - ', $request->event_time);
        $start_time = $time[0];
        $end_time = $time[1];*/

        $event = Event::find($request->event_id);
        $old_smart_list_id = $event->smart_list_id;
        $old_attendee_quota = $event->attendee_quota; // Eski kotayı sakla

        $event->marketo_event_id = $request->marketo_event_id;
        $event->duration_pf_event = $request->duration_pf_event;
        $event->event_description = $request->event_description;
        $event->contact_email = $request->contact_email;
        /*if ($request->duration_pf_event == 2) {
            $dates = explode(' - ', $request->event_date);
            $event->event_date = \DateTime::createFromFormat('d/m/Y', $dates[0])->format('Y-m-d');
            $event->event_end_date = \DateTime::createFromFormat('d/m/Y', $dates[1])->format('Y-m-d');
        } else {
            $event->event_date = \DateTime::createFromFormat('d/m/Y', $request->event_date)->format('Y-m-d');
        }
        $event->event_start_time = $start_time;
        $event->event_end_time = $end_time;*/
        if ($file) {
            $event->event_banner = $event_banner_file_name;
        }
        $event->event_title = $request->event_title;
        $event->event_location = $request->event_location;
        $event->business_unit = $request->business_unit;
        $event->smart_list_id = $request->smart_list_id;
        $event->attendee_quota = $request->attendee_quota;
        $event->moderators_count = $request->moderators_count;
        $event->speakers_count = $request->speakers_count;
        $event->status = $request->submit_type ? $request->submit_type : 1;
        $event->user_check_in = $request->user_check_in ? 1 : 0;
        $event->badge_creation = $request->badge_creation ? 1 : 0;
        $event->attendees_info = $request->attendees_info ? 1 : 0;

        // Attendee quota artırıldıysa is_completed'i 1 yap (yeniden düzenlenebilir)
        if ($request->attendee_quota > $old_attendee_quota && $event->is_completed == 2) {
            $event->is_completed = 1;
        }

        $event->save();

        if ($request->event_date) {

            EventDate::query()->where('event_id', $event->id)->delete();

            foreach ($request->event_date as $key => $date) {

                $event_dates = new EventDate();
                $event_dates->event_id = $event->id;

                $event_dates->event_date = \DateTime::createFromFormat('d/m/Y', $date)->format('Y-m-d');

                $time = explode(' - ', $request->event_time[$key]);
                $start_time = $time[0];
                $end_time = $time[1];
                $event_dates->event_start_time = $start_time;
                $event_dates->event_end_time = $end_time;

                $event_dates->event_date_timezone = $request->event_date_timezones[$key];
                $event_dates->event_location = $request->event_location[$key];
                $event_dates->event_location_url = $request->event_location_url[$key];
                $event_dates->event_country = $request->event_country[$key];

                $event_dates->save();
            }
        }
        if ($request->session_time_duration_input) {
            $session_time_duration_input = explode(',', $request->session_time_duration_input);
            $session_title_input = explode(',', $request->session_title_input);
            $session_speakers_input = explode(',', $request->session_speakers_input);
            $session_moderators_input = explode(',', $request->session_moderators_input);
            $session_dates_input = $request->session_dates_input ? explode(',', $request->session_dates_input) : null;

            $eventSessionOld = EventSession::where('event_id', $event->id)->get();
            EventSession::where('event_id', $event->id)->delete();
            $oldSessions = $eventSessionOld->keyBy('session_title');

            foreach ($session_time_duration_input as $key => $item) {
                // $event_sessions = EventSession::find($event->id);
                $event_sessions = new EventSession();
                $event_sessions->event_id = $event->id;
                $event_sessions->session_time_duration = ltrim($session_time_duration_input[$key]);
                $event_sessions->session_title = ltrim($session_title_input[$key]);
                $event_sessions->session_speakers = str_replace('<br>', '-', ltrim($session_speakers_input[$key]));
                $event_sessions->session_moderators = isset($session_moderators_input[$key]) ? str_replace('<br>', '-', ltrim($session_moderators_input[$key])) : NULL;
                $event_sessions->session_dates = $request->session_dates_input ? ltrim($session_dates_input[$key]) : '';
                $event_sessions->save();
            }
            $newSessions = EventSession::where('event_id', $event->id)->get()->keyBy('session_title');
            // Session register tablosunda eski event ile yeni eventte session_title aynı olanın session_idsini güncelle
            foreach ($oldSessions as $title => $oldSession) {
                if (isset($newSessions[$title])) {
                    SessionRegister::where('session_id', $oldSession->id)
                        ->update(['session_id' => $newSessions[$title]->id]);
                }
            }
        }

        if ($request->multiple_document_name) {
            $multiple_document_name = explode(',', $request->multiple_document_name);
            $multiple_document_type = explode(',', $request->multiple_document_type);
            $multiple_selected_session = explode(',', $request->multiple_selected_session);

            foreach ($multiple_selected_session as $key => $item) {
                $file = $request->file('duplicated_document')[$key];
                $doc_file_name = Str::slug($multiple_document_name[$key], '_') . '.' . $file->getClientOriginalExtension();
                $path = $file->storeAs('public/event_documents', $doc_file_name);

                $event_documents = new EventDocument;
                $event_documents->event_id = $event->id;
                $event_documents->document_name = $doc_file_name;
                $event_documents->document_type = $multiple_document_type[$key];
                $event_documents->uploaded_document = $request->duplicated_document[$key];
                $event_documents->selected_session = $multiple_selected_session[$key];
                $event_documents->save();
            }
        }

        // Handle speakers - first delete all existing speakers for this event
        EventModeratorSpearker::where('event_id', $event->id)->where('type', 1)->delete();

        if ($request->speaker_name && $request->speaker_name[0]) {
            foreach ($request->speaker_name as $key => $item) {
                if (!empty(trim($item))) { // Only save if name is not empty
                    $event_speaker = new EventModeratorSpearker;

                    $doc_file_name = "";
                    if (isset($request->file('speaker_banner')[$key])) {
                        $file = $request->file('speaker_banner')[$key];
                        $doc_file_name = Str::slug($event->id . '_' . $request->speaker_name[$key], '_') . '.' . $file->getClientOriginalExtension();
                        if (strpos($file->getClientOriginalName(), $doc_file_name) === false) {
                            $doc_file_name = date('His') . '_' . $doc_file_name;
                            $path = $file->storeAs('public/evet_moderator_speaker', $doc_file_name);
                        }
                    }

                    $event_speaker->event_id = $event->id;
                    $event_speaker->type = 1;
                    $event_speaker->name = $request->speaker_name[$key];
                    $event_speaker->title = $request->speaker_title[$key] ?? '';
                    $event_speaker->banner = $doc_file_name;
                    $event_speaker->description = $request->speaker_description[$key] ?? '';
                    $event_speaker->save();
                }
            }
        }

        // Handle moderators - first delete all existing moderators for this event
        EventModeratorSpearker::where('event_id', $event->id)->where('type', 2)->delete();

        if ($request->moderator_name && $request->moderator_name[0]) {
            foreach ($request->moderator_name as $key => $item) {
                if (!empty(trim($item))) { // Only save if name is not empty
                    $event_moderator = new EventModeratorSpearker;

                    $doc_file_name = "";
                    if (isset($request->file('moderator_banner')[$key])) {
                        $file = $request->file('moderator_banner')[$key];
                        $doc_file_name = Str::slug($event->id . '_' . $request->moderator_name[$key], '_') . '.' . $file->getClientOriginalExtension();

                        if (strpos($file->getClientOriginalName(), $doc_file_name) === false) {
                            $doc_file_name = date('His') . '_' . $doc_file_name;
                            $path = $file->storeAs('public/evet_moderator_moderator', $doc_file_name);
                        }
                    }

                    $event_moderator->event_id = $event->id;
                    $event_moderator->type = 2;
                    $event_moderator->name = $request->moderator_name[$key];
                    $event_moderator->title = $request->moderator_title[$key] ?? '';
                    $event_moderator->banner = $doc_file_name;
                    $event_moderator->description = $request->moderator_description[$key] ?? '';
                    $event_moderator->save();
                }
            }
        }


        if ($old_smart_list_id != $request->smart_list_id) {
            $users = get_user_list($request->smart_list_id);
            if ($users) {
                foreach ($users as $user) {
                    $register = Register::where('user_id', $user->id)
                        ->where('event_id', $event->id)
                        ->first();

                    if ($register) {
                        $register->update([
                            'first_name' => $user->firstName,
                            'last_name' => $user->lastName,
                            'company' => $user->company,
                            'email' => $user->email,
                            'title' => $user->title,
                            'phone' => $user->phone,
                            'status' => 0,
                            'check_in' => 0
                        ]);
                    } else {
                        $uuid = base64_encode($user->email);

                        $register = Register::create([
                            'uuid' => $uuid,
                            'event_id' => $event->id,
                            'user_id' => $user->id,
                            'first_name' => $user->firstName,
                            'last_name' => $user->lastName,
                            'company' => $user->company,
                            'email' => $user->email,
                            'title' => $user->title,
                            'phone' => $user->phone,
                            'status' => 0,
                            'check_in' => 0
                        ]);
                    }
                }

                $short_list = Register::where('event_id', $event->id)
                    ->orderBy('id') // sırayla seç
                    ->limit($event->attendee_quota)
                    ->get();

                if ($short_list) {
                    foreach ($short_list as $user) {
                        $user->status = 1;
                        $user->save();
                    }
                }
            }
        }

        if ($request->submit_type == 1) {
            return redirect('/admin/event/' . $request->event_id . '/register-list')->with('success', 'Your event has been updated');
        } else {
            return redirect('/admin/event-detail/' . $request->event_id)->with('success', 'Your event has been updated');
        }
    }

    public function event_detail($id)
    {
        $event = Event::query()->findOrFail($id);
        $time_zones = TimeZone::all()->sortBy('time_zone');
        $countries = Country::all();
        $business_units = BusinessUnit::all();
        return view('event.event_detail.overview', ['event' => $event, 'business_units' => $business_units, 'time_zones' => $time_zones, 'countries' => $countries]);
    }

    public function attendees_info($event_id)
    {
        $event = Event::query()->findOrFail($event_id);

        // Check if attendees_info is enabled for this event
        if (!$event->attendees_info) {
            return redirect()->route('panel.event_detail', ['event_id' => $event_id])
                ->with('error', 'Attendees Info is not enabled for this event.');
        }

        // Get only shortlisted users (status = 1)
        $users = Register::query()
            ->where('event_id', $event_id)
            ->where('status', 1)
            ->get();

        return view('event.event_detail.attendees_info', compact('event', 'users'));
    }

    public function event_manuelcheckin__or__qrcheckin($event_id)
    {
        $event = Event::query()->findOrFail($event_id);
        $registers = Register::query('')->where('event_id', $event_id)->where('status', 1)->get();

        // Session sorted by time
        $eventSessions = $event->sessions->sortBy(function ($session) {
            try {
                $date = \Carbon\Carbon::createFromFormat('d/m/Y', $session->session_dates);
                [$start, $end] = array_map('trim', explode('-', $session->session_time_duration));
                $startTime = \Carbon\Carbon::createFromFormat('g:i A', $start);
                $endTime = \Carbon\Carbon::createFromFormat('g:i A', $end);
                return [$date->timestamp, $startTime->timestamp, $endTime->timestamp];
            } catch (\Exception $e) {
                // Fallback to current timestamp if parsing fails
                return [now()->timestamp, 0, 0];
            }
        });

        $event->sessions = $eventSessions->values()->all();

        return view('event.event_manuelcheckin__or__qrcheckin', compact(['registers', 'event']));
    }

    public function event_manuelcheckin__or__qrcheckin_search(Request $request, $event_id)
    {
        $search = $request->search;

        $query = Register::where('event_id', $event_id);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('first_name', 'like', '%' . $search . '%')
                    ->orWhere('last_name', 'like', '%' . $search . '%')
                    ->orWhere('email', 'like', '%' . $search . '%')
                    ->orWhere('company', 'like', '%' . $search . '%')
                    ->orWhere('title', 'like', '%' . $search . '%');
            });
        }

        $registers = $query->where('status', 1)->get();

        $event = Event::query()->findOrFail($event_id);

        return view('event.event_manuelcheckin__or__qrcheckin', compact('registers', 'event'));
    }

    public function deleteSession($id)
    {
        $session = EventSession::query()->findOrFail($id);
        if ($session) {
            $session->delete();
            return response()->json(['success' => 'Session deleted successfully']);
        }
        return response()->json(['error' => 'Session not found'], 404);
    }
    public function deleteDocument($id)
    {
        $document = EventDocument::query()->findOrFail($id);
        if ($document) {
            $document->delete();
            return response()->json(['success' => 'Document deleted successfully']);
        }
        return response()->json(['error' => 'Document not found'], 404);
    }

    public function sessionCheckin()
    {
        $register = Register::query()->findOrFail(request()->register_id);
        $session = EventSession::query()->findOrFail(request()->session_id);
        $event = Event::query()->findOrFail($register->event_id);

        $sessionRegister = SessionRegister::query()->updateOrCreate(
            ['register_id' => $register->id, 'session_id' => $session->id, 'event_id' => $event->id],
            ['status' => request()->status]
        );

        $session_ids_raw = SessionRegister::select('session_id')->where('register_id', $register->id)->where('event_id', $event->id)->where('status', 1)->get()->toArray();
        $session_ids = ['session_id' => array_column($session_ids_raw, 'session_id')];

        $sessions = EventSession::select('session_title')->where('event_id', $event->id)->whereIn('id', $session_ids['session_id'])->get()->toArray();
        $session_titles = implode('; ', array_column($sessions, 'session_title'));

        if ($session_titles) {
            user_check_in_session_manuel($register, $session_titles);
        }

        return response()->json(['success' => 'Session check-in updated successfully']);
    }

    public function sessionCheckinQr()
    {
        $register = Register::query()->findOrFail(request()->register_id);
        $session = EventSession::query()->findOrFail(request()->session_id);
        $event = Event::query()->findOrFail(request()->event_id);

        $sessionRegister = SessionRegister::query()->updateOrCreate(
            ['register_id' => $register->id, 'session_id' => $session->id, 'event_id' => $event->id],
            ['status' => request()->status]
        );

        $session_ids_raw = SessionRegister::select('session_id')->where('register_id', $register->id)->where('event_id', $event->id)->where('status', 1)->get()->toArray();
        $session_ids = ['session_id' => array_column($session_ids_raw, 'session_id')];

        $sessions = EventSession::select('session_title')->where('event_id', $event->id)->whereIn('id', $session_ids['session_id'])->get()->toArray();
        $session_titles = implode('; ', array_column($sessions, 'session_title'));

        if ($session_titles) {
            user_check_in_session_manuel($register, $session_titles);
        }

        return response()->json([
            'success' => true,
            'message' => 'Check-in Successfully',
            'redirect_url' => url('/admin/event/' . $event->id . '/badge-creation-qr/' . $register->id),
        ]);
    }
}
