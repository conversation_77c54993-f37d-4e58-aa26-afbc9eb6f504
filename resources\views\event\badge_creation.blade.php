@extends('layouts.app')
@section('content')

    <div class="checkout">
        <div class="card shadow-none border">
            <div class="card-body p-4">
                @include('layouts.event_details')
                @include('layouts.page_navigation')

                <div class="mb-4">
                    <h5 class="fw-bold">Badge Template Design</h5>
                    <p class="text-muted">Create your badge using our template design</p>
                </div>

                {{-- template design --}}
                @if(count($badge_templates) > 0)
                    @include('event.badge_creation_template', [
                        'badge_template_1' => $badge_templates[0] ?? null,
                        'register' => $register ?? null,
                        'event' => $event
                    ])
                @else
                    @include('event.badge_creation_template', ['register' => $register ?? null, 'event' => $event]);
                @endif
                {{-- template design end --}}
            </div>
        </div>
    </div>

@endsection

@push('js')
    <script src="//cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Template Bg Area
            const templateBgButton = document.getElementById('templateBgButton');
            if (templateBgButton) {
                templateBgButton.addEventListener('click', function() {
                    document.getElementById('template_badge_bg_file').click();
                });
            }

            const templateBgFile = document.getElementById('template_badge_bg_file');
            if (templateBgFile) {
                templateBgFile.addEventListener('change', function(event) {
                    const file = event.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const previewContainer = document.querySelector('#preview-container-template');
                            if (previewContainer) {
                                previewContainer.style.backgroundImage = `url(${e.target.result})`;
                                previewContainer.style.backgroundRepeat = 'no-repeat';
                                previewContainer.style.backgroundPosition = 'center center';
                                previewContainer.style.setProperty('background-size', '288px 432px', 'important');
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // Template Logo Area
            const templateLogoButton = document.getElementById('templateLogoButton');
            if (templateLogoButton) {
                templateLogoButton.addEventListener('click', function() {
                    document.getElementById('template_badge_logo_file').click();
                });
            }

            const templateLogoFile = document.getElementById('template_badge_logo_file');
            if (templateLogoFile) {
                templateLogoFile.addEventListener('change', function(event) {
                    const file = event.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            const previewContainer = document.querySelector('#preview-container-template');
                            if (previewContainer) {
                                const existingLogo = previewContainer.querySelector('.template-logo');
                                if (existingLogo) {
                                    existingLogo.remove();
                                }

                                const logoImage = document.createElement('img');
                                logoImage.src = e.target.result;
                                logoImage.className = 'template-logo';
                                logoImage.style.position = 'absolute';
                                logoImage.style.top = '50px';
                                logoImage.style.left = '50%';
                                logoImage.style.width = '150px';
                                logoImage.style.height = '60px';
                                logoImage.style.objectFit = 'contain';
                                logoImage.style.transform = 'translateX(-50%)';
                                logoImage.style.zIndex = '10';

                                previewContainer.appendChild(logoImage);
                            }
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }
        });
    </script>

    <script>
        // Template form validation
        document.addEventListener('DOMContentLoaded', function() {
            const templateForm = document.querySelector('#template_form');
            if (templateForm) {
                templateForm.addEventListener('submit', function(event) {
                    const templateBgFileInput = document.getElementById('template_badge_bg_file');
                    {{--
                    if (templateBgFileInput && !templateBgFileInput.files.length) {
                        event.preventDefault();
                        Swal.fire({
                            icon: 'warning',
                            title: 'Background file required',
                            text: 'Please upload a background file before submitting the form.',
                        });
                        return;
                    }
                    --}}

                    if (templateBgFileInput && templateBgFileInput.files.length > 0) {
                        var file = templateBgFileInput.files[0];
                        var fileExtension = file.name.split('.').pop().toLowerCase();
                        var validExtensions = ["png", "jpg", "jpeg"];

                        if (validExtensions.indexOf(fileExtension) === -1) {
                            Swal.fire({
                                title: "Invalid file type",
                                text: "Please select a valid image file (PNG, JPG, JPEG).",
                                icon: "warning"
                            });
                            event.preventDefault();
                        }
                    }
                });
            }
        });
    </script>

    @isset($register)
        <script>
            $(document).ready(function() {
                // Register bilgilerini güvenli şekilde güncelle
                const registerData = {
                    first_name: '{{ $register->first_name }}',
                    last_name: '{{ $register->last_name }}',
                    country: '{{ $register->country }}'
                };

                // Template preview'ları güncelle
                $('.template_first_name, .template_preview_fn').text(registerData.first_name + ' ' + registerData.last_name);
                $('.template_company_name, .template_preview_company').text(registerData.country);

                // QR kod ekle
                const qrImageSrc = '/qr/{{ $register->user_id }}.png';
                const templatePreview = $('#preview-container-template');

                if (templatePreview.length > 0) {
                    // Mevcut QR kodunu kaldır
                    templatePreview.find('.qr-image').remove();

                    // Yeni QR kod ekle
                    const qrImage = $('<img>', {
                        src: qrImageSrc,
                        class: 'qr-image d-flex justify-content-center align-items-center',
                        css: {
                            //width: '25%',
                            //position: 'absolute',
                            //bottom: '1250px',
                            //right: '200px'
                        }
                    });
                    templatePreview.append(qrImage);
                }
            });
        </script>
    @endisset




@endpush
